# Facebook Style Lightbox Gallery - Installation Guide

## Quick Installation

1. **Upload the Plugin**
   - Copy the entire `facebook-lightbox-gallery` folder to your WordPress `/wp-content/plugins/` directory
   - Or zip the folder and upload through WordPress admin

2. **Activate the Plugin**
   - Go to WordPress Admin → Plugins
   - Find "Facebook Style Lightbox Gallery"
   - Click "Activate"

3. **That's It!**
   - The plugin works automatically on all images
   - No configuration needed

## Features Overview

### ✅ Automatic Image Detection
- Works with all images in posts, pages, and galleries
- No need to add special classes or attributes
- Automatically applies to existing content

### ✅ Facebook-Style Interface
- Clean, modern lightbox design
- Image navigation with arrows
- Responsive layout for all devices

### ✅ Like/Dislike System
- Users can like or dislike any image
- Likes are saved permanently in database
- Real-time updates without page refresh
- Shows total likes and dislikes

### ✅ Comments System
- Full commenting functionality
- User avatars and names
- Timestamps (e.g., "2 hours ago")
- Scrollable comment list

### ✅ User Integration
- Works with WordPress user system
- Requires login for interactions (configurable)
- User avatars from Gravatar

## How It Works

1. **Image Click**: User clicks any image on your site
2. **Lightbox Opens**: Facebook-style modal appears
3. **Image Display**: Full-size image with navigation
4. **Social Features**: Like/dislike buttons and comment form
5. **Real-time Updates**: All interactions saved instantly

## Database Tables

The plugin creates two tables:
- `wp_flg_likes` - Stores image likes/dislikes
- `wp_flg_comments` - Stores image comments

## Customization

### CSS Customization
Edit `/assets/css/lightbox-style.css` to customize appearance:
- Colors and themes
- Button styles
- Layout adjustments
- Mobile responsiveness

### Settings (Optional)
You can modify these options in your WordPress database:
- `flg_enable_likes` - Enable/disable likes (default: 1)
- `flg_enable_comments` - Enable/disable comments (default: 1)
- `flg_require_login` - Require login for interactions (default: 0)

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Troubleshooting

### Images Not Opening in Lightbox
- Check if jQuery is loaded
- Verify plugin is activated
- Check browser console for JavaScript errors

### Likes/Comments Not Saving
- Ensure users are logged in (if required)
- Check database permissions
- Verify AJAX is working

### Styling Issues
- Check for CSS conflicts with theme
- Verify CSS file is loading
- Use browser developer tools to debug

## Performance

The plugin is optimized for performance:
- CSS and JS only load when needed
- Minimal database queries
- Efficient AJAX handling
- No external dependencies

## Security

- All AJAX requests use WordPress nonces
- User input is sanitized
- Database queries use prepared statements
- No external API calls

## Support

For issues or questions:
1. Check this installation guide
2. Review the plugin code
3. Test with default WordPress theme
4. Check browser console for errors

## File Structure

```
facebook-lightbox-gallery/
├── facebook-lightbox-gallery.php (Main plugin file)
├── includes/
│   ├── class-lightbox-core.php (Core functionality)
│   ├── class-database.php (Database operations)
│   └── class-ajax-handler.php (AJAX handling)
├── assets/
│   ├── css/
│   │   └── lightbox-style.css (Styling)
│   └── js/
│       └── lightbox-script.js (JavaScript)
├── templates/
│   └── lightbox-template.php (HTML template)
├── readme.txt (WordPress plugin info)
└── INSTALLATION.md (This file)
```
