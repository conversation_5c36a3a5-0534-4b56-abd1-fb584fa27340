/* Facebook Style Lightbox CSS */

/* Gallery Grid */
.flg-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.flg-gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.flg-gallery-item:hover {
    transform: scale(1.02);
}

.flg-gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
}

/* Images with lightbox */
img[data-flg-lightbox="true"] {
    cursor: pointer;
    transition: opacity 0.2s ease;
}

img[data-flg-lightbox="true"]:hover {
    opacity: 0.9;
}

/* Lightbox Overlay */
.flg-lightbox-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.flg-lightbox-overlay.active {
    display: flex;
    opacity: 1;
}

/* Lightbox Container */
.flg-lightbox-container {
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Lightbox Content */
.flg-lightbox-content {
    display: flex;
    max-width: 1200px;
    max-height: 90vh;
    width: 100%;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Image Section */
.flg-lightbox-image-section {
    flex: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
    position: relative;
}

.flg-lightbox-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Navigation Arrows */
.flg-nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.8);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.flg-nav-arrow:hover {
    background: rgba(255, 255, 255, 1);
}

.flg-nav-prev {
    left: 20px;
}

.flg-nav-next {
    right: 20px;
}

/* Sidebar */
.flg-lightbox-sidebar {
    flex: 1;
    min-width: 350px;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    background: #fff;
}

/* Header */
.flg-lightbox-header {
    padding: 16px;
    border-bottom: 1px solid #e4e6ea;
}

.flg-image-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #1c1e21;
}

.flg-image-caption {
    font-size: 14px;
    color: #65676b;
    margin: 0;
}

/* Actions */
.flg-lightbox-actions {
    padding: 12px 16px;
    border-bottom: 1px solid #e4e6ea;
    display: flex;
    gap: 12px;
}

.flg-action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    background: #f0f2f5;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background 0.2s ease;
}

.flg-action-btn:hover {
    background: #e4e6ea;
}

.flg-action-btn.active {
    background: #1877f2;
    color: white;
}

.flg-action-btn.dislike.active {
    background: #e41e3f;
    color: white;
}

/* Comments Section */
.flg-comments-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.flg-comments-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.flg-comment {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.flg-comment-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    flex-shrink: 0;
}

.flg-comment-content {
    flex: 1;
}

.flg-comment-bubble {
    background: #f0f2f5;
    padding: 8px 12px;
    border-radius: 16px;
    margin-bottom: 4px;
}

.flg-comment-author {
    font-weight: 600;
    font-size: 13px;
    color: #1c1e21;
    margin-bottom: 2px;
}

.flg-comment-text {
    font-size: 14px;
    color: #1c1e21;
    line-height: 1.3;
}

.flg-comment-time {
    font-size: 12px;
    color: #65676b;
    margin-left: 12px;
}

/* Comment Form */
.flg-comment-form {
    padding: 16px;
    border-top: 1px solid #e4e6ea;
    display: flex;
    gap: 8px;
}

.flg-comment-input {
    flex: 1;
    border: 1px solid #ccd0d5;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    outline: none;
    resize: none;
    min-height: 36px;
    max-height: 100px;
}

.flg-comment-input:focus {
    border-color: #1877f2;
}

.flg-comment-submit {
    background: #1877f2;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background 0.2s ease;
}

.flg-comment-submit:hover {
    background: #166fe5;
}

.flg-comment-submit:disabled {
    background: #e4e6ea;
    color: #bcc0c4;
    cursor: not-allowed;
}

/* Close Button */
.flg-close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: background 0.2s ease;
}

.flg-close-btn:hover {
    background: rgba(255, 255, 255, 1);
}

/* Loading State */
.flg-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    font-size: 16px;
    color: #65676b;
}

/* Responsive Design */
@media (max-width: 768px) {
    .flg-lightbox-content {
        flex-direction: column;
        max-height: 95vh;
    }
    
    .flg-lightbox-sidebar {
        min-width: auto;
        max-width: none;
        max-height: 50vh;
    }
    
    .flg-lightbox-image-section {
        min-height: 300px;
    }
    
    .flg-nav-arrow {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
    
    .flg-nav-prev {
        left: 10px;
    }
    
    .flg-nav-next {
        right: 10px;
    }
}
