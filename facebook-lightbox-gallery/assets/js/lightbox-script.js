/**
 * Facebook Style Lightbox JavaScript
 */

(function($) {
    'use strict';
    
    let currentImageId = null;
    let allImages = [];
    let currentIndex = 0;
    
    $(document).ready(function() {
        initLightbox();
    });
    
    function initLightbox() {
        // Find all images with lightbox attribute
        $('img[data-flg-lightbox="true"]').on('click', function(e) {
            e.preventDefault();
            
            const imageId = $(this).data('flg-image-id');
            const fullSrc = $(this).data('flg-full-src') || $(this).attr('src');
            
            // Build array of all images for navigation
            buildImageArray();
            
            // Find current image index
            currentIndex = allImages.findIndex(img => img.id == imageId);
            if (currentIndex === -1) currentIndex = 0;
            
            openLightbox(imageId, fullSrc);
        });
        
        // Close lightbox events
        $(document).on('click', '.flg-close-btn, .flg-lightbox-overlay', function(e) {
            if (e.target === this) {
                closeLightbox();
            }
        });
        
        // Prevent closing when clicking inside content
        $(document).on('click', '.flg-lightbox-content', function(e) {
            e.stopPropagation();
        });
        
        // Navigation events
        $(document).on('click', '.flg-nav-prev', function() {
            navigateImage(-1);
        });
        
        $(document).on('click', '.flg-nav-next', function() {
            navigateImage(1);
        });
        
        // Keyboard navigation
        $(document).on('keydown', function(e) {
            if ($('.flg-lightbox-overlay').hasClass('active')) {
                switch(e.keyCode) {
                    case 27: // Escape
                        closeLightbox();
                        break;
                    case 37: // Left arrow
                        navigateImage(-1);
                        break;
                    case 39: // Right arrow
                        navigateImage(1);
                        break;
                }
            }
        });
        
        // Like/Dislike events
        $(document).on('click', '.flg-like-btn', function() {
            toggleLike('like');
        });
        
        $(document).on('click', '.flg-dislike-btn', function() {
            toggleLike('dislike');
        });
        
        // Comment form events
        $(document).on('submit', '.flg-comment-form', function(e) {
            e.preventDefault();
            submitComment();
        });
        
        $(document).on('keypress', '.flg-comment-input', function(e) {
            if (e.which === 13 && !e.shiftKey) {
                e.preventDefault();
                submitComment();
            }
        });
    }
    
    function buildImageArray() {
        allImages = [];
        $('img[data-flg-lightbox="true"]').each(function() {
            const imageId = $(this).data('flg-image-id');
            const fullSrc = $(this).data('flg-full-src') || $(this).attr('src');
            
            allImages.push({
                id: imageId,
                src: fullSrc,
                element: this
            });
        });
    }
    
    function openLightbox(imageId, imageSrc) {
        currentImageId = imageId;
        
        // Show overlay
        $('.flg-lightbox-overlay').addClass('active');
        $('body').css('overflow', 'hidden');
        
        // Load image data
        loadImageData(imageId, imageSrc);
    }
    
    function closeLightbox() {
        $('.flg-lightbox-overlay').removeClass('active');
        $('body').css('overflow', '');
        currentImageId = null;
    }
    
    function navigateImage(direction) {
        if (allImages.length <= 1) return;
        
        currentIndex += direction;
        
        if (currentIndex >= allImages.length) {
            currentIndex = 0;
        } else if (currentIndex < 0) {
            currentIndex = allImages.length - 1;
        }
        
        const nextImage = allImages[currentIndex];
        openLightbox(nextImage.id, nextImage.src);
    }
    
    function loadImageData(imageId, imageSrc) {
        // Show loading state
        $('.flg-lightbox-sidebar').html('<div class="flg-loading">Loading...</div>');
        $('.flg-lightbox-image').attr('src', imageSrc);
        
        // Update navigation visibility
        $('.flg-nav-arrow').toggle(allImages.length > 1);
        
        // Load data via AJAX
        $.post(flg_ajax.ajax_url, {
            action: 'flg_get_image_data',
            image_id: imageId,
            nonce: flg_ajax.nonce
        }, function(response) {
            if (response.success) {
                renderSidebar(response.data);
            } else {
                $('.flg-lightbox-sidebar').html('<div class="flg-loading">Error loading image data</div>');
            }
        });
    }
    
    function renderSidebar(data) {
        const sidebarHtml = `
            <div class="flg-lightbox-header">
                <h3 class="flg-image-title">${data.image_title || 'Image'}</h3>
                ${data.image_caption ? `<p class="flg-image-caption">${data.image_caption}</p>` : ''}
            </div>
            
            <div class="flg-lightbox-actions">
                <button class="flg-action-btn flg-like-btn ${data.user_status === 'like' ? 'active' : ''}" ${!data.can_interact ? 'disabled' : ''}>
                    👍 <span class="flg-like-count">${data.likes}</span>
                </button>
                <button class="flg-action-btn flg-dislike-btn ${data.user_status === 'dislike' ? 'active dislike' : ''}" ${!data.can_interact ? 'disabled' : ''}>
                    👎 <span class="flg-dislike-count">${data.dislikes}</span>
                </button>
            </div>
            
            <div class="flg-comments-section">
                <div class="flg-comments-list">
                    ${renderComments(data.comments)}
                </div>
                
                ${data.can_interact ? `
                <form class="flg-comment-form">
                    <textarea class="flg-comment-input" placeholder="Write a comment..." rows="1"></textarea>
                    <button type="submit" class="flg-comment-submit">Post</button>
                </form>
                ` : '<div style="padding: 16px; text-align: center; color: #65676b;">Please log in to comment</div>'}
            </div>
        `;
        
        $('.flg-lightbox-sidebar').html(sidebarHtml);
    }
    
    function renderComments(comments) {
        if (!comments || comments.length === 0) {
            return '<div style="text-align: center; color: #65676b; padding: 20px;">No comments yet</div>';
        }
        
        return comments.map(comment => `
            <div class="flg-comment">
                <img src="${comment.user_avatar}" alt="${comment.user_name}" class="flg-comment-avatar">
                <div class="flg-comment-content">
                    <div class="flg-comment-bubble">
                        <div class="flg-comment-author">${comment.user_name}</div>
                        <div class="flg-comment-text">${comment.comment_text}</div>
                    </div>
                    <div class="flg-comment-time">${comment.created_at}</div>
                </div>
            </div>
        `).join('');
    }
    
    function toggleLike(likeType) {
        if (!flg_ajax.is_logged_in && flg_ajax.login_required) {
            alert('Please log in to like images.');
            return;
        }
        
        $.post(flg_ajax.ajax_url, {
            action: 'flg_toggle_like',
            image_id: currentImageId,
            like_type: likeType,
            nonce: flg_ajax.nonce
        }, function(response) {
            if (response.success) {
                // Update like counts
                $('.flg-like-count').text(response.data.likes);
                $('.flg-dislike-count').text(response.data.dislikes);
                
                // Update button states
                $('.flg-like-btn').removeClass('active');
                $('.flg-dislike-btn').removeClass('active dislike');
                
                if (response.data.user_status === 'like') {
                    $('.flg-like-btn').addClass('active');
                } else if (response.data.user_status === 'dislike') {
                    $('.flg-dislike-btn').addClass('active dislike');
                }
            } else {
                alert(response.data || 'Error processing like');
            }
        });
    }
    
    function submitComment() {
        const commentText = $('.flg-comment-input').val().trim();
        
        if (!commentText) {
            return;
        }
        
        if (!flg_ajax.is_logged_in) {
            alert('Please log in to comment.');
            return;
        }
        
        // Disable form
        $('.flg-comment-submit').prop('disabled', true);
        
        $.post(flg_ajax.ajax_url, {
            action: 'flg_add_comment',
            image_id: currentImageId,
            comment_text: commentText,
            nonce: flg_ajax.nonce
        }, function(response) {
            if (response.success) {
                // Clear input
                $('.flg-comment-input').val('');
                
                // Update comments list
                $('.flg-comments-list').html(renderComments(response.data.comments));
                
                // Scroll to bottom
                $('.flg-comments-list').scrollTop($('.flg-comments-list')[0].scrollHeight);
            } else {
                alert(response.data || 'Error adding comment');
            }
        }).always(function() {
            $('.flg-comment-submit').prop('disabled', false);
        });
    }
    
})(jQuery);
