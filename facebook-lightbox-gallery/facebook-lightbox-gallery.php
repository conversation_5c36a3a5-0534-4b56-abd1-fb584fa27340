<?php
/**
 * Plugin Name: Facebook Style Lightbox Gallery
 * Plugin URI: https://yourwebsite.com/facebook-lightbox-gallery
 * Description: A WordPress plugin that adds Facebook-style lightbox with likes and comments to all images automatically.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 * Text Domain: facebook-lightbox-gallery
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('FLG_PLUGIN_URL', plugin_dir_url(__FILE__));
define('FLG_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('FLG_VERSION', '1.0.0');

// Main plugin class
class FacebookLightboxGallery {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Load required files
        $this->load_dependencies();
        
        // Initialize components
        new FLG_Lightbox_Core();
        new FLG_Database();
        new FLG_Ajax_Handler();
        
        // Load text domain for translations
        load_plugin_textdomain('facebook-lightbox-gallery', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    private function load_dependencies() {
        require_once FLG_PLUGIN_PATH . 'includes/class-lightbox-core.php';
        require_once FLG_PLUGIN_PATH . 'includes/class-database.php';
        require_once FLG_PLUGIN_PATH . 'includes/class-ajax-handler.php';
    }
    
    public function activate() {
        // Create database tables
        $database = new FLG_Database();
        $database->create_tables();
        
        // Set default options
        add_option('flg_enable_likes', 1);
        add_option('flg_enable_comments', 1);
        add_option('flg_require_login', 0);
    }
    
    public function deactivate() {
        // Clean up if needed
        // Note: We don't drop tables on deactivation to preserve data
    }
}

// Initialize the plugin
new FacebookLightboxGallery();
