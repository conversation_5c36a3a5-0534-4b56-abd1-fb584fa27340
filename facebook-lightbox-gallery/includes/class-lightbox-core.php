<?php
/**
 * Core Lightbox Functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class FLG_Lightbox_Core {
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'add_lightbox_html'));
        add_filter('the_content', array($this, 'process_images'));
        add_filter('post_gallery', array($this, 'process_gallery'), 10, 2);
    }
    
    public function enqueue_scripts() {
        // Enqueue CSS
        wp_enqueue_style(
            'flg-lightbox-style',
            FLG_PLUGIN_URL . 'assets/css/lightbox-style.css',
            array(),
            FLG_VERSION
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'flg-lightbox-script',
            FLG_PLUGIN_URL . 'assets/js/lightbox-script.js',
            array('jquery'),
            FLG_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('flg-lightbox-script', 'flg_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('flg_nonce'),
            'user_id' => get_current_user_id(),
            'is_logged_in' => is_user_logged_in(),
            'login_required' => get_option('flg_require_login', 0)
        ));
    }
    
    public function process_images($content) {
        // Find all img tags and add lightbox attributes
        $pattern = '/<img[^>]+>/i';
        preg_match_all($pattern, $content, $matches);
        
        if (!empty($matches[0])) {
            foreach ($matches[0] as $img_tag) {
                // Extract src attribute
                preg_match('/src="([^"]+)"/i', $img_tag, $src_match);
                if (!empty($src_match[1])) {
                    $image_url = $src_match[1];
                    $image_id = $this->get_attachment_id_by_url($image_url);
                    
                    // Add lightbox attributes
                    $new_img_tag = str_replace('<img', '<img data-flg-lightbox="true" data-flg-image-id="' . $image_id . '"', $img_tag);
                    $content = str_replace($img_tag, $new_img_tag, $content);
                }
            }
        }
        
        return $content;
    }
    
    public function process_gallery($output, $attr) {
        // Handle WordPress galleries
        $post = get_post();
        
        static $instance = 0;
        $instance++;
        
        if (!empty($attr['ids'])) {
            $attachments = get_posts(array(
                'include' => $attr['ids'],
                'post_status' => 'inherit',
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'order' => $attr['order'],
                'orderby' => $attr['orderby']
            ));
        } else {
            $attachments = get_children(array(
                'post_parent' => $post->ID,
                'post_status' => 'inherit',
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'order' => $attr['order'],
                'orderby' => $attr['orderby']
            ));
        }
        
        if (empty($attachments)) {
            return '';
        }
        
        $gallery_html = '<div class="flg-gallery">';
        
        foreach ($attachments as $attachment) {
            $image_url = wp_get_attachment_image_src($attachment->ID, 'medium');
            $full_image_url = wp_get_attachment_image_src($attachment->ID, 'full');
            
            $gallery_html .= '<div class="flg-gallery-item">';
            $gallery_html .= '<img src="' . $image_url[0] . '" data-flg-lightbox="true" data-flg-image-id="' . $attachment->ID . '" data-flg-full-src="' . $full_image_url[0] . '" alt="' . get_post_meta($attachment->ID, '_wp_attachment_image_alt', true) . '">';
            $gallery_html .= '</div>';
        }
        
        $gallery_html .= '</div>';
        
        return $gallery_html;
    }
    
    private function get_attachment_id_by_url($url) {
        global $wpdb;
        $attachment = $wpdb->get_col($wpdb->prepare("SELECT ID FROM {$wpdb->posts} WHERE guid='%s';", $url));
        return !empty($attachment) ? $attachment[0] : 0;
    }
    
    public function add_lightbox_html() {
        include FLG_PLUGIN_PATH . 'templates/lightbox-template.php';
    }
}
