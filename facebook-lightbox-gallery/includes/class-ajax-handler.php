<?php
/**
 * AJAX Request Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class FLG_Ajax_Handler {
    
    private $database;
    
    public function __construct() {
        $this->database = new FLG_Database();
        
        // AJAX actions for logged in users
        add_action('wp_ajax_flg_toggle_like', array($this, 'handle_toggle_like'));
        add_action('wp_ajax_flg_add_comment', array($this, 'handle_add_comment'));
        add_action('wp_ajax_flg_get_image_data', array($this, 'handle_get_image_data'));
        
        // AJAX actions for non-logged in users (if allowed)
        add_action('wp_ajax_nopriv_flg_get_image_data', array($this, 'handle_get_image_data'));
        
        if (!get_option('flg_require_login', 0)) {
            add_action('wp_ajax_nopriv_flg_toggle_like', array($this, 'handle_toggle_like_guest'));
            add_action('wp_ajax_nopriv_flg_add_comment', array($this, 'handle_add_comment_guest'));
        }
    }
    
    public function handle_toggle_like() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'flg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error('You must be logged in to like images.');
            return;
        }
        
        $image_id = intval($_POST['image_id']);
        $like_type = sanitize_text_field($_POST['like_type']);
        $user_id = get_current_user_id();
        
        if (!in_array($like_type, array('like', 'dislike'))) {
            wp_send_json_error('Invalid like type.');
            return;
        }
        
        $result = $this->database->toggle_like($image_id, $user_id, $like_type);
        $likes_data = $this->database->get_image_likes($image_id);
        $user_status = $this->database->get_user_like_status($image_id, $user_id);
        
        wp_send_json_success(array(
            'action' => $result,
            'likes' => $likes_data['likes'],
            'dislikes' => $likes_data['dislikes'],
            'user_status' => $user_status
        ));
    }
    
    public function handle_toggle_like_guest() {
        wp_send_json_error('Please log in to like images.');
    }
    
    public function handle_add_comment() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'flg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error('You must be logged in to comment.');
            return;
        }
        
        $image_id = intval($_POST['image_id']);
        $comment_text = sanitize_textarea_field($_POST['comment_text']);
        $user_id = get_current_user_id();
        
        if (empty($comment_text)) {
            wp_send_json_error('Comment cannot be empty.');
            return;
        }
        
        $result = $this->database->add_comment($image_id, $user_id, $comment_text);
        
        if ($result) {
            $comments = $this->database->get_image_comments($image_id, 5);
            wp_send_json_success(array(
                'message' => 'Comment added successfully.',
                'comments' => $this->format_comments($comments)
            ));
        } else {
            wp_send_json_error('Failed to add comment.');
        }
    }
    
    public function handle_add_comment_guest() {
        wp_send_json_error('Please log in to comment.');
    }
    
    public function handle_get_image_data() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'flg_nonce')) {
            wp_die('Security check failed');
        }
        
        $image_id = intval($_POST['image_id']);
        
        // Get image metadata
        $image_meta = wp_get_attachment_metadata($image_id);
        $image_url = wp_get_attachment_image_src($image_id, 'full');
        $image_title = get_the_title($image_id);
        $image_caption = wp_get_attachment_caption($image_id);
        
        // Get likes and comments
        $likes_data = $this->database->get_image_likes($image_id);
        $comments = $this->database->get_image_comments($image_id, 5);
        
        $user_status = null;
        if (is_user_logged_in()) {
            $user_status = $this->database->get_user_like_status($image_id, get_current_user_id());
        }
        
        wp_send_json_success(array(
            'image_url' => $image_url[0],
            'image_title' => $image_title,
            'image_caption' => $image_caption,
            'likes' => $likes_data['likes'],
            'dislikes' => $likes_data['dislikes'],
            'user_status' => $user_status,
            'comments' => $this->format_comments($comments),
            'can_interact' => is_user_logged_in() || !get_option('flg_require_login', 0)
        ));
    }
    
    private function format_comments($comments) {
        $formatted = array();
        
        foreach ($comments as $comment) {
            $formatted[] = array(
                'id' => $comment->id,
                'user_name' => $comment->display_name,
                'user_avatar' => get_avatar_url($comment->user_email, array('size' => 32)),
                'comment_text' => $comment->comment_text,
                'created_at' => human_time_diff(strtotime($comment->created_at)) . ' ago'
            );
        }
        
        return $formatted;
    }
}
