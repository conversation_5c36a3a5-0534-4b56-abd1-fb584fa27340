<?php
/**
 * Database Operations
 */

if (!defined('ABSPATH')) {
    exit;
}

class FLG_Database {
    
    public function __construct() {
        // Constructor
    }
    
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Likes table
        $likes_table = $wpdb->prefix . 'flg_likes';
        $likes_sql = "CREATE TABLE $likes_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            image_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            like_type varchar(10) NOT NULL DEFAULT 'like',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_user_image (user_id, image_id),
            KEY image_id (image_id),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        // Comments table
        $comments_table = $wpdb->prefix . 'flg_comments';
        $comments_sql = "CREATE TABLE $comments_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            image_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            comment_text text NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY image_id (image_id),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($likes_sql);
        dbDelta($comments_sql);
    }
    
    public function get_image_likes($image_id) {
        global $wpdb;
        
        $likes_table = $wpdb->prefix . 'flg_likes';
        
        $likes = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $likes_table WHERE image_id = %d AND like_type = 'like'",
            $image_id
        ));
        
        $dislikes = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $likes_table WHERE image_id = %d AND like_type = 'dislike'",
            $image_id
        ));
        
        return array(
            'likes' => intval($likes),
            'dislikes' => intval($dislikes)
        );
    }
    
    public function get_user_like_status($image_id, $user_id) {
        global $wpdb;
        
        $likes_table = $wpdb->prefix . 'flg_likes';
        
        $status = $wpdb->get_var($wpdb->prepare(
            "SELECT like_type FROM $likes_table WHERE image_id = %d AND user_id = %d",
            $image_id,
            $user_id
        ));
        
        return $status;
    }
    
    public function toggle_like($image_id, $user_id, $like_type) {
        global $wpdb;
        
        $likes_table = $wpdb->prefix . 'flg_likes';
        
        // Check if user already liked/disliked this image
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT like_type FROM $likes_table WHERE image_id = %d AND user_id = %d",
            $image_id,
            $user_id
        ));
        
        if ($existing) {
            if ($existing === $like_type) {
                // Remove like/dislike if clicking the same button
                $wpdb->delete(
                    $likes_table,
                    array('image_id' => $image_id, 'user_id' => $user_id),
                    array('%d', '%d')
                );
                return 'removed';
            } else {
                // Update to new like type
                $wpdb->update(
                    $likes_table,
                    array('like_type' => $like_type),
                    array('image_id' => $image_id, 'user_id' => $user_id),
                    array('%s'),
                    array('%d', '%d')
                );
                return 'updated';
            }
        } else {
            // Insert new like/dislike
            $wpdb->insert(
                $likes_table,
                array(
                    'image_id' => $image_id,
                    'user_id' => $user_id,
                    'like_type' => $like_type
                ),
                array('%d', '%d', '%s')
            );
            return 'added';
        }
    }
    
    public function add_comment($image_id, $user_id, $comment_text) {
        global $wpdb;
        
        $comments_table = $wpdb->prefix . 'flg_comments';
        
        $result = $wpdb->insert(
            $comments_table,
            array(
                'image_id' => $image_id,
                'user_id' => $user_id,
                'comment_text' => sanitize_textarea_field($comment_text)
            ),
            array('%d', '%d', '%s')
        );
        
        return $result !== false;
    }
    
    public function get_image_comments($image_id, $limit = 10, $offset = 0) {
        global $wpdb;
        
        $comments_table = $wpdb->prefix . 'flg_comments';
        
        $comments = $wpdb->get_results($wpdb->prepare(
            "SELECT c.*, u.display_name, u.user_email 
             FROM $comments_table c 
             LEFT JOIN {$wpdb->users} u ON c.user_id = u.ID 
             WHERE c.image_id = %d 
             ORDER BY c.created_at DESC 
             LIMIT %d OFFSET %d",
            $image_id,
            $limit,
            $offset
        ));
        
        return $comments;
    }
}
