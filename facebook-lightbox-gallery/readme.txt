=== Facebook Style Lightbox Gallery ===
Contributors: yourname
Tags: lightbox, gallery, facebook, images, likes, comments
Requires at least: 5.0
Tested up to: 6.4
Stable tag: 1.0.0
Requires PHP: 7.4
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

A WordPress plugin that adds Facebook-style lightbox with likes and comments to all images automatically.

== Description ==

Facebook Style Lightbox Gallery transforms your WordPress site's image viewing experience by automatically adding a beautiful, Facebook-inspired lightbox to all images in posts, pages, and galleries.

**Key Features:**

* **Automatic Image Detection** - Works with all images without any setup
* **Facebook-Style Interface** - Clean, modern design inspired by Facebook's photo viewer
* **Like/Dislike System** - Users can like or dislike images with persistent storage
* **Comments System** - Full commenting functionality for each image
* **User Integration** - Seamlessly works with WordPress user system
* **Responsive Design** - Perfect on desktop, tablet, and mobile devices
* **Keyboard Navigation** - Navigate with arrow keys and escape to close
* **AJAX Powered** - Smooth interactions without page reloads

**How It Works:**

1. Install and activate the plugin
2. All images on your site automatically get lightbox functionality
3. Users can click any image to open the Facebook-style lightbox
4. Like, dislike, and comment on images
5. All interactions are saved and visible to other users

**Perfect For:**

* Photography websites
* Portfolio sites
* Blog sites with image content
* Community sites
* Any WordPress site with images

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/facebook-lightbox-gallery` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress
3. The plugin works automatically - no configuration needed!

== Frequently Asked Questions ==

= Does this work with all images? =

Yes! The plugin automatically detects and adds lightbox functionality to all images in posts, pages, and WordPress galleries.

= Do users need to be logged in to like images? =

By default, yes. However, you can modify the plugin settings to allow guest interactions.

= Will this slow down my site? =

No, the plugin is optimized for performance and only loads resources when needed.

= Is it mobile-friendly? =

Absolutely! The lightbox is fully responsive and works great on all devices.

= Can I customize the appearance? =

Yes, you can modify the CSS file to match your site's design.

== Screenshots ==

1. Facebook-style lightbox with image, likes, and comments
2. Mobile responsive design
3. Gallery grid view
4. Comment system in action

== Changelog ==

= 1.0.0 =
* Initial release
* Facebook-style lightbox interface
* Like/dislike functionality
* Comments system
* Automatic image detection
* Responsive design
* Keyboard navigation

== Upgrade Notice ==

= 1.0.0 =
Initial release of Facebook Style Lightbox Gallery.

== Technical Details ==

**Database Tables Created:**
* `wp_flg_likes` - Stores user likes and dislikes for images
* `wp_flg_comments` - Stores user comments for images

**AJAX Endpoints:**
* `flg_toggle_like` - Handle like/dislike actions
* `flg_add_comment` - Add new comments
* `flg_get_image_data` - Load image data and interactions

**Browser Support:**
* Chrome (latest)
* Firefox (latest)
* Safari (latest)
* Edge (latest)
* Internet Explorer 11+

== Support ==

For support, please visit our website or contact us through the WordPress support forums.

== Privacy ==

This plugin stores user interactions (likes and comments) in your WordPress database. No data is sent to external services.
