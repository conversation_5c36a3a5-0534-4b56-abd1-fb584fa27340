#!/bin/bash

# Facebook Style Lightbox Gallery - Release Creator
# This script creates a new version zip file

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Facebook Style Lightbox Gallery - Release Creator${NC}"
echo "=================================================="

# Get current version from main plugin file
CURRENT_VERSION=$(grep "Version:" facebook-lightbox-gallery/facebook-lightbox-gallery.php | sed 's/.*Version: //' | tr -d ' ')

if [ -z "$CURRENT_VERSION" ]; then
    echo -e "${RED}❌ Error: Could not find version in plugin file${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 Current Version: ${CURRENT_VERSION}${NC}"

# Create zip filename
ZIP_NAME="facebook-lightbox-gallery-v${CURRENT_VERSION}.zip"

# Remove existing zip if it exists
if [ -f "$ZIP_NAME" ]; then
    echo -e "${YELLOW}🗑️  Removing existing zip file...${NC}"
    rm "$ZIP_NAME"
fi

# Create new zip
echo -e "${BLUE}📦 Creating zip file: ${ZIP_NAME}${NC}"
zip -r "$ZIP_NAME" facebook-lightbox-gallery/ -x "*.DS_Store" "*/.*"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Successfully created: ${ZIP_NAME}${NC}"
    echo -e "${GREEN}📁 File size: $(du -h "$ZIP_NAME" | cut -f1)${NC}"
    echo ""
    echo -e "${BLUE}📥 Download URL:${NC}"
    echo -e "${GREEN}file://$(pwd)/${ZIP_NAME}${NC}"
    echo ""
    echo -e "${YELLOW}🔗 Or copy this path:${NC}"
    echo "$(pwd)/${ZIP_NAME}"
else
    echo -e "${RED}❌ Error creating zip file${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}📋 Release Summary:${NC}"
echo "==================="
echo "Version: ${CURRENT_VERSION}"
echo "File: ${ZIP_NAME}"
echo "Location: $(pwd)"
echo ""
echo -e "${GREEN}🎉 Release ready for download!${NC}"
